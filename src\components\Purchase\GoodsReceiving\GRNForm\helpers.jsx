import { notification, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import Constants, { DEFAULT_CUR_ROUND_OFF, toISTDate } from '@Apis/constants';
import PRZSelect from '../../../Common/UI/PRZSelect';
import HideValue from '@Components/Common/RestrictedAccess/HideValue';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';

const { Option } = Select;

const SelectOrdersForGRN = ({ disabled, grnTypeValue, mode, onChange, selectedPoValue, purchaseOrders, isDataMaskingPolicyEnable, isHideCostPrice, MONEY, loading, inputClassName, getPurchaseOrdersV2, selectedTenant, selectedTenantSeller }) => {

  const [searchByPONumber, setSearchByPONumber] = useState('');

  useEffect(() => {
    const handler = setTimeout(() => {
      const excludePo = grnTypeValue === 'MULTIPO';
      getPurchaseOrdersV2({
        query: {
          tenant_id: selectedTenant,
          tenant_seller_id: selectedTenantSeller,
          status: 'ISSUED',
          page: 1,
          limit: 30,
          exclude_job_works_po: excludePo,
          exclude_subcontractor_po: excludePo,
          search_keyword: searchByPONumber,
        },
      });
    }, 500); // 500ms debounce time
    return () => {
      clearTimeout(handler);
    };
  }, [searchByPONumber]);
  return (
    <PRZSelect
      getPopupContainer={(triggerNode) => triggerNode.parentNode}
      placeholder="Select purchase order.."
      labelInValue
      disabled={disabled}
      showSearch
      // allowClear
      value={selectedPoValue}
      maxTagCount="responsive"
      mode={mode}
      onChange={onChange}
      loading={loading}
      className={inputClassName}
      onSearch={(value) => setSearchByPONumber(value)}
      filterOption={false}
    >
      {purchaseOrders?.purchase_order
        ?.filter((item) => item?.closing_status !== 'CLOSED')
        ?.map((item) => (
          <Option key={item.po_id} value={item.po_id}>
            {`#${item.po_number} (${toISTDate(item.created_date || item.created_at).format(
              'DD/MM/YYYY'
            )}) - `}
            {isDataMaskingPolicyEnable && isHideCostPrice ? (
              <HideValue
                showPopOver
                popOverMessage={'You don\'t have access to view po amount'}
              />
            ) : (
              MONEY(
                item.po_grand_total,
                item?.org_currency_info?.currency_code
              )
            )}
          </Option>
        ))}
    </PRZSelect>
  );
};

export default SelectOrdersForGRN;

export const calculateCostPrice = (item, selectedPo, isApInvoiceEnabled) => {
  // If AP Invoice is enabled, cost price should be 0
  if (isApInvoiceEnabled) {
    return 0;
  }

  // If no offer price, return 0
  if (!item?.offer_price) {
    return 0;
  }

  // If conversion rate exists, apply it and round to default currency precision
  if (selectedPo?.conversion_rate) {
    const convertedPrice = item.offer_price * selectedPo.conversion_rate;
    return Number.parseFloat(convertedPrice.toFixed(DEFAULT_CUR_ROUND_OFF));
  }

  // Return the original offer price
  return item.offer_price;
};

export const getClassNameHelper = (isFlexible, isOverflow, item, formSubmitted) => {
  if (formSubmitted && (Number(item?.received_qty) <= 0)) {
    return 'orgFormInputError';
  }
  if (isFlexible) {
    return '';
  }
  if (formSubmitted && (Number(item?.quantity) - Number(item?.total_received_qty) - Number(item?.received_qty) < 0) || isOverflow) {
    return 'orgFormInputError';
  }

  return '';
};

export const getLineTotals = (
  data,
  chargeData,
  charge1Value,
  grnTypeValue,
  taxTypeInfo,
  taxTypeName,
  freightTax,
  freightTaxData
) => {
  data = data ?? [];
  chargeData = chargeData ?? [];
  charge1Value = charge1Value ?? 0;

  let totalAmount = 0;
  let totalDiscount = 0;
  let totalBase = 0;
  let totalTcs = 0;
  let totalTds = 0;
  let totalTax = 0;
  let totalOtherCharge = 0;
  let totalOtherChargeWithOutTax = 0;
  let grnTotal = 0;
  let totalTaxValue = 0;
  let freightCharge = Number(charge1Value);
  let totalOtherChargesForTaxableAmount = 0;
  let freightTaxAmount = 0;

  // Taxes bifurcation
  if (data?.length > 0) {
    totalTaxValue = Helpers.groupAndSumByTaxName(
      data?.map((item) => item?.child_taxes)?.flat()
    )?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
  }

  for (let i = 0; i < chargeData?.length; i++) {
    let currentOtherCharge = 0;
    let currentOtherChargeWithoutTax = 0;
    let chargesTaxAmountLinesWise = 0;

    if (chargeData[i]?.charge_amount) {
      chargesTaxAmountLinesWise = Helpers.groupAndSumByTaxName(
        [...(chargeData[i]?.chargesTaxData?.child_taxes?.flat() || [])]
      )?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

      currentOtherCharge = chargeData?.[i]?.chargesTax
        ? chargeData?.[i]?.charge_amount + chargesTaxAmountLinesWise
        : chargeData?.[i]?.charge_amount;

      currentOtherChargeWithoutTax = chargeData?.[i]?.charge_amount;

      if (chargeData?.[i]?.chargesTax) {
        totalOtherChargesForTaxableAmount += chargeData?.[i]?.charge_amount;
      }
    }

    totalOtherCharge += currentOtherCharge;
    totalOtherChargeWithOutTax += currentOtherChargeWithoutTax;
  }

  let lines = [];

  for (let i = 0; i < data?.length; i++) {
    let currentAmount = 0;
    let currentDiscount = 0;
    let currentBase = 0;
    let currentTax = 0;
    let currentGRN = 0;

    const quantityToUse =
      grnTypeValue === 'ADHOC' ? data[i]?.quantity : data[i]?.received_qty;

    const discountValue = Number(data[i].discount);

    if (quantityToUse) {
      currentAmount += quantityToUse * data[i].offer_price;

      if (discountValue) {
        if (data[i]?.lineDiscountType === 'Percent') {
          currentDiscount +=
            quantityToUse * data[i].offer_price * (discountValue / 100);

          currentBase =
            (quantityToUse * data[i].offer_price * (100 - discountValue)) / 100;
        } else if (data[i]?.lineDiscountType === 'Amount') {
          currentDiscount += discountValue;
          currentBase += quantityToUse * data[i].offer_price - discountValue;
        }
      } else {
        currentDiscount += 0;
        currentBase += quantityToUse * data[i].offer_price;
      }
    }

    // Subtract TDS from currentBase or Add TCS
    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;
      const tcsAmount = currentBase * tcsRate;
      totalTcs += tcsAmount;
    } else if (taxTypeInfo && taxTypeName === 'TDS') {
      const tdsRate = taxTypeInfo?.tax_value / 100;
      const tdsAmount = currentBase * tdsRate;
      totalTds -= tdsAmount;
    }

    if (data[i]?.taxInfo?.tax_value || data[i]?.taxInfo?.[0]?.tax_value) {
      currentTax =
        currentBase *
        ((data[i]?.taxInfo?.tax_value || data[i]?.taxInfo?.[0]?.tax_value) /
          100);
    }

    if (currentBase) currentGRN = currentBase;

    totalAmount += currentAmount;
    totalDiscount += currentDiscount;
    totalBase += currentBase;
    totalTax += currentTax;
    grnTotal += currentGRN;

    lines.push({
      grn_line_total: currentGRN + currentTax + (taxTypeName === 'TCS' ? totalTcs : totalTds),
      grn_line_quantity: quantityToUse,
      grn_line_unit_price: data[i].offer_price,
    });
  }

  grnTotal += totalTaxValue;

  lines = lines.map((line) => {
    let unitOtherCharge = 0;
    let unitFreightCharge = 0;
    const unitCostPrice = Number(line.grn_line_unit_price);

    unitOtherCharge = Number(
      ((line.grn_line_total / grnTotal) * totalOtherChargeWithOutTax) /
      line.grn_line_quantity
    );

    unitFreightCharge = Number(
      ((line.grn_line_total / grnTotal) * Number(charge1Value)) /
      line.grn_line_quantity
    );

    return {
      ...line,
      unit_landed_cost:
        Number(unitCostPrice + unitOtherCharge + unitFreightCharge).toFixed(2) ||
        0,
      unit_other_cost: Number(unitOtherCharge).toFixed(2) || 0,
      unit_freight_cost: Number(unitFreightCharge).toFixed(2) || 0,
    };
  });

  if (freightTax) {
    freightTaxAmount = Helpers.groupAndSumByTaxName(
      [...freightTaxData?.child_taxes?.flat()]
    )?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

    totalBase += freightCharge;
    freightCharge += freightTaxAmount;

    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;
      totalTcs = totalBase * tcsRate;
    } else if (taxTypeInfo && taxTypeName === 'TDS') {
      const tdsRate = taxTypeInfo?.tax_value / 100;
      totalTds = -((totalBase + totalOtherChargesForTaxableAmount) * tdsRate);
    }
  }

  if (taxTypeInfo && taxTypeName === 'TCS') {
    const tcsRate = taxTypeInfo?.tax_value / 100;
    const tcsAmount =
      (totalBase +
        totalTaxValue +
        Number(totalOtherCharge) +
        (freightTax ? freightTaxAmount : freightCharge)) *
      tcsRate;
    totalTcs = tcsAmount;
  }

  grnTotal += taxTypeName === 'TCS' ? totalTcs : totalTds;
  grnTotal += totalOtherCharge;
  grnTotal += freightCharge;
  totalBase += totalOtherChargesForTaxableAmount;

  return {
    totalAmount,
    totalDiscount,
    totalBase,
    totalTax,
    totalTcs,
    totalTds,
    grnTotal,
    lines,
  };
};

export function isUpdateValidData({
  grnData,
  grnDate,
  ewayBillNumber,
  ewayBillList,
  purchaseAccount,
  invoiceNumber,
  selectedPo,
  selectedTenant,
  user,
  selectedPoForGrn,
  getLineTotals,
}) {
  let isValid = true;

  const isApInvoiceEnabled =
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled &&
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;

  for (let i = 0; i < grnData?.length; i++) {
    const item = grnData[i];
    const quantity = Number(item?.quantity);
    const grnPercent = Number(item?.product_sku_info?.grn_over_flow_percent);
    const overFlowQuantity = ((grnPercent / 100) * quantity) + quantity;

    const checkOverflow = () => {
      if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po) {
        if (grnPercent > 0) {
          return (overFlowQuantity - item.total_received_qty - item.received_qty) < 0;
        } else if (grnPercent === 0 || grnPercent === null) {
          return (overFlowQuantity - item.total_received_qty - item.received_qty) < 0;
        }
      } else if (grnPercent > 0) {
        return (overFlowQuantity - item.total_received_qty - item.received_qty) < 0;
      } else if (grnPercent === 0 || grnPercent === null) {
        return (quantity - item.total_received_qty - item.received_qty) < 0;
      }
      return false;
    };

    if (
      checkOverflow() ||
      Number(item.received_qty) <= 0 ||
      (isApInvoiceEnabled
        ? !item?.offer_price && item?.offer_price !== 0
        : Number(item.offer_price) <= 0) ||
      !grnDate ||
      ((user?.user_tenants?.find((k) =>
        k?.tenant_id === (selectedTenant || selectedPoForGrn?.tenant_id || selectedPo?.tenant_id || user?.tenant_info?.tenant_id)
      )?.tally_connection_status &&
        (user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' ||
          user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP'))
        ? !item?.tally_purchase_account
        : false) ||
      !CustomFieldHelpers.isCfValid(item?.lineCustomFields)
    ) {
      isValid = false;
      break;
    }
  }

  if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory && getLineTotals().totalAmount >= 50_000 && user?.tenant_info?.country_code === 'IN' && (!ewayBillNumber || !ewayBillList?.length)) {
    isValid = false;
  }

  if (!purchaseAccount &&
    user?.tenant_info?.purchase_account_selection === 'FROM_GRN' &&
    !isApInvoiceEnabled) {
    isValid = false;
  }

  if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory &&
    !invoiceNumber) {
    isValid = false;
  }

  return isValid;
}

export function isCreateValidData({
  grnData,
  grnDate,
  ewayBillNumber,
  ewayBillList,
  purchaseAccount,
  invoiceNumber,
  grnTypeValue,
  selectedPo,
  selectedTenant,
  selectedPoForGrn,
  user,
  getLineTotals,
  getGrnErrors
}) {
  let isValid = true;

  const isApInvoiceEnabled =
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled &&
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;

  if (!grnDate) return false;
  if (grnTypeValue === 'ADHOC') return false;

  for (let i = 0; i < grnData?.length; i++) {
    const item = grnData[i];
    const quantity = Number(item?.quantity);
    const grnPercent = Number(item?.product_sku_info?.grn_over_flow_percent || 0);
    const overFlowQuantity = Math.floor((grnPercent / 100) * quantity + quantity);

    function checkOverflow() {
      const grnConfig = user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings;

      if (grnConfig?.flexible_qty_wrt_po) {
        if (grnPercent > 0) {
          return (overFlowQuantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0;
        } else if (grnPercent === 0 || grnPercent === null) {
          return (overFlowQuantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0 ? false : undefined;
        }
      } else if (grnPercent > 0) {
        return (overFlowQuantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0;
      } else if (grnPercent === 0 || grnPercent === null) {
        return (quantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0;
      }
    }

    if (
      checkOverflow() ||
      Number(item.received_qty) <= 0 ||
      (isApInvoiceEnabled
        ? !item?.offer_price && item?.offer_price !== 0
        : Number(item.offer_price) <= 0) ||
      !grnDate ||
      (
        user?.user_tenants?.find(
          (k) =>
            k?.tenant_id ===
            (selectedTenant ||
              selectedPoForGrn?.tenant_id ||
              selectedPo?.tenant_id)
        )?.tally_connection_status &&
          (
            user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' ||
            user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP'
          )
          ? !item?.tally_purchase_account
          : false
      ) ||
      !CustomFieldHelpers.isCfValid(item?.lineCustomFields)
    ) {
      return false;
    }

    if (item?.product_sku_info?.product_type === 'STORABLE' && !item?.selectedBatch) {
      return false;
    }
  }

  const grnSettings = user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings;

  if (grnSettings?.is_e_way_bill_mandatory && getLineTotals().totalAmount >= 50_000 && user?.tenant_info?.country_code === 'IN' && (!ewayBillNumber || !ewayBillList?.length)) return false;

  if (!purchaseAccount && user?.tenant_info?.purchase_account_selection === 'FROM_GRN' && !isApInvoiceEnabled) {
    return false;
  }

  if (grnSettings?.grn_invoice_number_mandatory && !invoiceNumber) {
    return false;
  }

  const { docLevelError, lineLevelError } = getGrnErrors();
  if (docLevelError?.length > 0 || lineLevelError?.length > 0) {
    return false;
  }

  return isValid;
}

export function isCreateValidDataADHOC({
  grnData,
  grnDate,
  purchaseAccount,
  invoiceNumber,
  selectedSellerId,
  selectedSeller,
  grnTypeValue,
  vendorAddress,
  ewayBillNumber,
  ewayBillList,
  selectedGRN,
  selectedTenant,
  user,
  getLineTotals,
  getGrnErrors,
}) {
  let isValid = true;

  const isApInvoiceEnabled =
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled &&
    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;

  // Basic seller + vendor validation
  if ((!selectedSeller?.seller_id && !selectedSellerId) || !vendorAddress) {
    isValid = false;
  }

  if (!grnDate) return false;

  // Line-level validation
  for (let i = 0; i < grnData?.length; i++) {
    const item = grnData[i];

    if (
      Number(item.received_qty) <= 0 ||
      (isApInvoiceEnabled
        ? !item?.offer_price && item?.offer_price !== 0
        : Number(item.offer_price) <= 0) ||
      !grnDate ||
      (
        user?.user_tenants?.find((k) => k?.tenant_id === selectedTenant)
          ?.tally_connection_status &&
          (
            user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' ||
            user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP'
          )
          ? !item?.tally_purchase_account
          : false
      ) ||
      !CustomFieldHelpers.isCfValid(item?.lineCustomFields)
    ) {
      return false;
    }

    // For storable items → batch required
    if (item?.product_sku_info?.product_type === 'STORABLE' && !item?.selectedBatch) {
      return false;
    }
  }

  // Purchase account required in some configs
  if (
    !purchaseAccount &&
    user?.tenant_info?.purchase_account_selection === 'FROM_GRN' &&
    !isApInvoiceEnabled
  ) {
    return false;
  }

  // Invoice number mandatory (for ADHOC or GRN-type entity)
  const grnSettings = user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings;
  if (
    (grnSettings?.grn_invoice_number_mandatory && !invoiceNumber) ||
    (grnSettings?.grn_invoice_number_mandatory &&
      !invoiceNumber &&
      (grnTypeValue === 'ADHOC' ||
        selectedGRN?.grn_entity_type === 'GOOD_RECEIVING_NOTE'))
  ) {
    return false;
  }

  // E-way bill mandatory for India if totalAmount >= 50k
  if (grnSettings?.is_e_way_bill_mandatory && getLineTotals().totalAmount >= 50_000 && user?.tenant_info?.country_code === 'IN' && (!ewayBillNumber || !ewayBillList?.length)) {
    return false;
  }

  // Error aggregation
  const { docLevelError, lineLevelError } = getGrnErrors();
  if (docLevelError?.length > 0 || lineLevelError?.length > 0) {
    return false;
  }

  return isValid;
}

export function isDataValid2(chargeData) {
  if (!chargeData?.length) return true;

  for (const charge of chargeData) {
    if (!charge.charge_name || !charge.charge_amount) {
      return false;
    }
  }

  return true;
}

export function evaluateDependentFields(cf, cfMap) {
  if (!cf?.dependentFields?.length) return;

  for (const dependentFieldId of cf.dependentFields) {
    const dependentField = cfMap[dependentFieldId];
    if (dependentField?.defaultExpression) {
      try {
        // inline expression evaluator
        const expression = dependentField.defaultExpression;
        const evaluatedExpression = expression.replaceAll(/{{cf_(\d+)}}/g, (match, p1) => {
          const field = cfMap[p1];
          if (!field) return 0;
          return Number(field.fieldValue || 0);
        });

        const evaluatedValue = eval(evaluatedExpression);
        dependentField.fieldValue = evaluatedValue;

        // 🔁 recursive evaluation for chained dependencies
        evaluateDependentFields(dependentField, cfMap);
      } catch (error) {
        console.error(
          `Failed to evaluate expression for field ${dependentFieldId}:`,
          error
        );
      }
    }
  }
}

export const computeUpdatedLineCFs = (
  lineCustomFields = [],
  cfId,
  fieldValue,
) => {
  const lineCfs = lineCustomFields?.map((cf) => ({ ...cf }));
  const target = lineCfs?.find((cf) => cf.cfId === cfId);
  if (!target) return lineCfs;

  target.fieldValue =
    target.fieldType === 'ATTACHMENT'
      ? transformAttachments(fieldValue)
      : fieldValue;

  const cfMap = Object.fromEntries(lineCfs?.map((cf) => [cf.cfId, cf]));
  evaluateDependentFields(target, cfMap);

  return lineCfs;
};

export const transformAttachments = (files = []) =>
  files?.map((a) => ({
    url: a?.response?.response?.location || a?.url,
    type: a?.type,
    name: a?.name,
    uid: a?.uid,
  }));

export const createData = ({
  tenantSku, dataItem, productData, inventoryLocations, selectedSeller, isLineWiseDiscount, discountPercentage, autoPrintDescription, discountType, cfV2DocGoodReceivingNotes, taxesGroup, selectedPoForGrn, user, billFromAddress, billToAddress,
}) => {
  const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;
  const lineCFs = CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes.data.document_line_custom_fields?.filter((item) => item?.is_active), false);

  const isVendorOverseas = (selectedSeller?.seller_info?.seller_type === 'OVERSEAS' || selectedPoForGrn?.seller_info?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
  const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes?.data?.batch_custom_fields, true);
  const newBatch = {
    tenant_product_id: tenantSku?.tenant_product_id,
    expiry_date: tenantSku?.product_info?.expiry_days > 0 ? (dayjs().endOf('day').add(tenantSku?.product_info?.expiry_days, 'day')) : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
    lot_number: '',
    custom_batch_number: `${tenantSku?.product_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${tenantSku?.product_info?.product_batch_counter}`,
    cost_price: isApInvoiceEnabled ? 0 : tenantSku?.cost_price || 0,
    mrp: tenantSku?.mrp || 0,
    selling_price: tenantSku?.selling_price || 0,
    uom_id: tenantSku?.uom_id,
    manual_entry: false,
    manufacturingDateFormat: tenantSku?.manufacturing_date_format,
    expiryDateFormat: tenantSku?.expiry_date_format,
    inventory_location_id: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
    inventory_location_path: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
    is_rejected_batch: false,
    seller_id: selectedSeller?.seller_info?.seller_id,
    seller_name: selectedSeller?.seller_info?.seller_name,
    custom_fields: batchCustomFields,
  };
  const copyDataItem = JSON.parse(JSON.stringify(dataItem));
  copyDataItem.secondaryUomUqc = tenantSku?.secondary_uom_info?.uqc;
  copyDataItem.secondaryAvailableQty = tenantSku?.secondary_available_qty;
  copyDataItem.secondary_uom_qty = 0;
  copyDataItem.secondaryUomId = tenantSku?.secondary_uom_id;
  copyDataItem.product_sku_name = tenantSku?.product_info.product_sku_name;
  copyDataItem.product_sku_id = tenantSku?.product_info.product_sku_id;
  copyDataItem.tenant_product_id = tenantSku?.tenant_product_id;
  copyDataItem.product_type = tenantSku?.product_type;
  copyDataItem.product_sku_info = tenantSku?.product_info;
  copyDataItem.manufacturingDateFormat = tenantSku?.manufacturing_date_format;
  copyDataItem.expiryDateFormat = tenantSku?.expiry_date_format;
  copyDataItem.quantity = 1;
  copyDataItem.unitPrice = isApInvoiceEnabled ? 0 : tenantSku?.selling_price || 0;
  copyDataItem.sku = tenantSku?.product_info?.product_sku_id || '';
  copyDataItem.taxInfo = (isVendorOverseas || isApInvoiceEnabled) ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : tenantSku?.tax_info;
  copyDataItem.child_taxes = Helpers.computeTaxation((1 * tenantSku?.cost_price), (isVendorOverseas || isApInvoiceEnabled) ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : tenantSku?.tax_info, , this.getBillToAddress())?.tax_info?.child_taxes;
  copyDataItem.taxId = (isVendorOverseas || isApInvoiceEnabled) ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : tenantSku?.product_info?.tax_id;
  copyDataItem.uomId = tenantSku?.purchase_uom_info?.uom_id || 0;
  copyDataItem.uom_info = tenantSku?.purchase_uom_info;
  copyDataItem.uomInfo = tenantSku?.purchase_uom_info;
  copyDataItem.uomGroup = tenantSku?.group_id || tenantSku?.purchase_uom_info?.group_id;
  copyDataItem.productCategoryInfo = tenantSku?.product_category_info || {};
  copyDataItem.uom_list = tenantSku?.uom_list;
  copyDataItem.selectedBatch = newBatch;
  copyDataItem.available_batches = tenantSku?.product_batches
    ? [
      {
        ...newBatch,
      },
      ...tenantSku?.product_batches.map((batch) => ({
        ...batch,
        custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
      })),
    ]
    : [
      {
        ...newBatch,
      },
    ];
  copyDataItem.tally_purchase_account = tenantSku?.tally_stock_group_name;
  copyDataItem.discount = isLineWiseDiscount ? discountPercentage : 0;
  copyDataItem.nextBatchCode = `${tenantSku?.internal_sku_code}/${dayjs().format('DDMMYY')}/${tenantSku?.product_batch_counter}`;
  copyDataItem.expiryDays = tenantSku?.product_info?.expiry_days;
  copyDataItem.received_qty = 1;
  copyDataItem.remarks = autoPrintDescription ? (productData?.description || '')?.replace(/<[^>]+>/g, '') : '';
  copyDataItem.remarkRequired = !!((autoPrintDescription && (productData?.description || '')?.replace(/<[^>]+>/g, '')));
  copyDataItem.lineDiscountType = isLineWiseDiscount ? discountType : 'Percent';
  copyDataItem.offer_price = isApInvoiceEnabled ? 0 : tenantSku?.cost_price || 0;
  copyDataItem.lineCustomFields = FormHelpers.lineSystemFieldValue(lineCFs, [...(tenantSku?.custom_fields ?? []), ...(productData?.system_custom_fields ?? [])])?.map((k) => ({
    ...k,
    fieldValue: k?.fieldName === 'Rate' ? copyDataItem.unitPrice : (k?.fieldName === 'Quantity' ? copyDataItem.quantity : k?.fieldValue),
  }));

  copyDataItem.multiple_batch_info = [{
    key: uuidv4(),
    ...newBatch,
    parentKey: copyDataItem.key,
    sq_no: 1,
    quantity: '',
    ar_number: '',
    expiryDays: tenantSku?.product_info?.expiry_days,
  }];

  return copyDataItem;
}